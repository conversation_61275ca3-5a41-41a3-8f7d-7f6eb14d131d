\documentclass[tikz,border=15pt]{standalone}
\usepackage[utf8]{inputenc}
\usepackage[T1]{fontenc}
\usepackage{CJKutf8}
\usepackage{tikz}
\usetikzlibrary{shapes,arrows,positioning,fit,backgrounds,calc}

\begin{document}
\begin{CJK}{UTF8}{gbsn}

% 定义样式
\tikzstyle{startstop} = [rectangle, rounded corners, minimum width=3cm, minimum height=1cm, text centered, draw=black, fill=red!30, font=\small]
\tikzstyle{process} = [rectangle, minimum width=3.5cm, minimum height=1.2cm, text centered, draw=black, fill=orange!30, font=\small]
\tikzstyle{decision} = [diamond, minimum width=3cm, minimum height=1cm, text centered, draw=black, fill=green!30, font=\small]
\tikzstyle{io} = [trapezium, trapezium left angle=70, trapezium right angle=110, minimum width=3.5cm, minimum height=1.2cm, text centered, draw=black, fill=blue!30, font=\small]
\tikzstyle{arrow} = [thick,->,>=stealth]

\begin{tikzpicture}[node distance=2.2cm]

% Start node
\node (start) [startstop] {开始};

% Environment initialization
\node (init) [process, below of=start] {环境初始化\\设置起点、目标、障碍物};

% State observation
\node (observe) [io, below of=init] {获取当前状态\\$\mathbf{s}_t = [x,y,z,v_x,v_y,v_z]$};

% DWA safe action generation
\node (dwa) [process, below of=observe] {DWA安全动作生成};

% DWA detailed steps - positioned to avoid overlap
\node (dw_calc) [process, left of=dwa, xshift=-7cm] {计算动态窗口\\$DW = [v_{min}, v_{max}]$};
\node (traj_pred) [process, below of=dw_calc, yshift=-0.5cm] {轨迹预测\\$\tau = \{p_1, p_2, ..., p_n\}$};
\node (safety_check) [decision, below of=traj_pred, yshift=-0.5cm] {安全检查\\$d_{obs} \geq d_{safe}$?};
\node (action_eval) [process, below of=safety_check, yshift=-0.5cm] {动作评估\\评分函数};

% TD3 decision - positioned with more space
\node (td3) [process, below of=dwa, yshift=-2cm] {TD3智能决策};

% TD3 detailed steps - repositioned to avoid overlap
\node (state_encode) [process, right of=td3, xshift=7cm] {状态编码\\$\mathbf{h}_{state} = Encoder(\mathbf{s})$};
\node (action_encode) [process, below of=state_encode, yshift=-0.5cm] {动作编码\\$\mathbf{h}_{action} = Encoder(\mathcal{A}_{safe})$};
\node (attention) [process, below of=action_encode, yshift=-0.5cm] {注意力机制\\$\mathbf{h} = Attention(\mathbf{h}_{state}, \mathbf{h}_{action})$};
\node (action_select) [process, below of=attention, yshift=-0.5cm] {动作选择\\$\mathbf{a} = \arg\max Q(\mathbf{s}, \mathbf{a})$};

% Action execution - positioned with adequate spacing
\node (execute) [process, below of=td3, yshift=-4cm] {执行选定动作\\$\mathbf{s}_{t+1} = f(\mathbf{s}_t, \mathbf{a}_t)$};

% Reward calculation
\node (reward) [process, below of=execute] {计算奖励\\$R_t = R_{goal} + R_{safety} + ...$};

% Experience storage
\node (store) [process, below of=reward] {存储经验\\$(\mathbf{s}_t, \mathbf{a}_t, R_t, \mathbf{s}_{t+1})$};

% Network training
\node (train) [decision, below of=store] {训练网络?};

% Training steps - repositioned to avoid overlap with main flow
\node (sample) [process, left of=train, xshift=-6cm] {采样批次\\$Batch \sim Buffer$};
\node (critic_update) [process, below of=sample, yshift=-0.5cm] {更新评论家\\$\min L_{critic}$};
\node (actor_update) [process, below of=critic_update, yshift=-0.5cm] {更新演员\\$\max J_{actor}$};
\node (target_update) [process, below of=actor_update, yshift=-0.5cm] {软更新目标网络\\$\theta' \leftarrow \tau\theta + (1-\tau)\theta'$};

% Termination condition check
\node (check_done) [decision, below of=train, yshift=-3cm] {到达目标\\或碰撞?};

% End
\node (end) [startstop, below of=check_done] {结束};

% Main flow connections
\draw [arrow] (start) -- (init);
\draw [arrow] (init) -- (observe);
\draw [arrow] (observe) -- (dwa);

% DWA to TD3 connection
\draw [arrow] (dwa) -- (td3);

% DWA internal flow - using safer routing to avoid overlaps
\draw [arrow] (dwa.west) -- ++(-1.5,0) |- (dw_calc.east);
\draw [arrow] (dw_calc) -- (traj_pred);
\draw [arrow] (traj_pred) -- (safety_check);
\draw [arrow] (safety_check) -- node[anchor=east] {是} (action_eval);
\draw [arrow] (safety_check.east) -- ++(2,0) |- node[anchor=south] {否} (traj_pred.east);
\draw [arrow] (action_eval.east) -- ++(3.5,0) |- (dwa.south);

% TD3 internal flow - using safer routing
\draw [arrow] (td3.east) -- ++(1.5,0) |- (state_encode.west);
\draw [arrow] (state_encode) -- (action_encode);
\draw [arrow] (action_encode) -- (attention);
\draw [arrow] (attention) -- (action_select);
\draw [arrow] (action_select.west) -- ++(-3.5,0) |- (td3.south);

% Continue main flow
\draw [arrow] (td3) -- (execute);
\draw [arrow] (execute) -- (reward);
\draw [arrow] (reward) -- (store);
\draw [arrow] (store) -- (train);

% Training branch - using safer routing to avoid main flow
\draw [arrow] (train.west) -- node[anchor=south] {是} (sample.east);
\draw [arrow] (sample) -- (critic_update);
\draw [arrow] (critic_update) -- (actor_update);
\draw [arrow] (actor_update) -- (target_update);
\draw [arrow] (target_update.east) -- ++(2.5,0) |- (check_done.west);

% No training branch
\draw [arrow] (train) -- node[anchor=west] {否} (check_done);

% Termination check
\draw [arrow] (check_done) -- node[anchor=east] {是} (end);
% Loop back connection - routed around all modules to avoid overlaps
\draw [arrow] (check_done.west) -- ++(-2,0) -- ++(0,-15) -- ++(-6,0) |- node[anchor=south] {否} (observe.west);

% Add background boxes with better positioning
\begin{scope}[on background layer]
\node[fit=(dw_calc)(traj_pred)(safety_check)(action_eval), fill=yellow!15, draw=black, dashed, inner sep=8pt, label={[label distance=5pt]above:DWA安全约束层}] {};
\node[fit=(state_encode)(action_encode)(attention)(action_select), fill=cyan!15, draw=black, dashed, inner sep=8pt, label={[label distance=5pt]above:TD3智能决策层}] {};
\node[fit=(sample)(critic_update)(actor_update)(target_update), fill=purple!15, draw=black, dashed, inner sep=8pt, label={[label distance=5pt]above:网络训练模块}] {};
\end{scope}

\end{tikzpicture}

\end{CJK}
\end{document}
